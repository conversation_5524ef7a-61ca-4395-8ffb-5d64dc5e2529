const { isEmpty, get, head, find, includes } = require('lodash');
const moment = require('moment');
const { errorHandler } = require('../utils/errorHandler');
const SubscriptionServices = require('../services/Subscription');
const subscriptionServices = new SubscriptionServices();
const StripePaymentsService = require('../services/StripePaymentsService');
const PlanServices = require('../services/Plan');
const planServices = new PlanServices();
const Stripe = require('../lib/services/stripe/Stripe');

const UserService = require('../services/UserService');
const UsersController = require('./UsersController');
const CRMService = require('../services/CRMService');

const userService = new UserService();

/**
 * @class Subscription Controller
 */

class SubscriptionController {
  static async fetch(request, h) {
    try {
      request.logger.info('SubscriptionController.fetch method called');
      const queryBuilder = await request.parsedQuery;
      const { where, options } = queryBuilder || {};
      const result = await subscriptionServices.fetch(where, options);
      if (isEmpty(result)) {
        request.logger.error('SubscriptionController.fetch method error');
        return h
          .response({
            statusCode: 400,
            message: 'Bad Request, Please try again',
            data: {},
          })
          .code(400);
      } else {
        request.logger.info(
          'SubscriptionController.fetch method called successfully',
        );
        return h
          .response({
            statusCode: 200,
            message: 'Subscription fetched successfully',
            data: result,
          })
          .code(200);
      }
    } catch (err) {
      request.logger.error(`SubscriptionController.fetch method error ${err}`);
      return errorHandler(err);
    }
  }

  static async update(request, h) {
    try {
      request.logger.info('SubscriptionController.update method called');
      const { id } = request.params;
      const payload = request.payload;
      const result = await subscriptionServices.update({ _id: id }, payload);
      if (isEmpty(result)) {
        request.logger.error('SubscriptionController.update method error');
        return h
          .response({
            statusCode: 400,
            message: 'Bad Request, Please try again',
            data: {},
          })
          .code(400);
      } else {
        request.logger.info(
          'SubscriptionController.update method called successfully',
        );
        return h
          .response({
            statusCode: 200,
            message: 'Subscription update successfully',
            data: result,
          })
          .code(200);
      }
    } catch (err) {
      request.logger.error(`SubscriptionController.update method error ${err}`);
      return errorHandler(err);
    }
  }

  static async delete(request, h) {
    try {
      request.logger.info('SubscriptionController.delete method called');
      const { id } = request.params;
      const result = await subscriptionServices.update(
        { _id: id },
        { status: 'inActive' },
      );
      if (isEmpty(result)) {
        request.logger.error('SubscriptionController.delete method error');
        return h
          .response({
            statusCode: 400,
            message: 'Bad Request, Please try again',
            data: {},
          })
          .code(400);
      } else {
        request.logger.info(
          'SubscriptionController.delete method called successfully',
        );
        return h
          .response({
            statusCode: 200,
            message: 'Subscription delete successfully',
            data: result,
          })
          .code(200);
      }
    } catch (err) {
      request.logger.error(`SubscriptionController.delete method error ${err}`);
      return errorHandler(err);
    }
  }

  static async paymentIntentSecret(request, h) {
    try {
      const id = request.payload.id;
      const priceId = request.payload.priceId;
      request.logger.info(
        `SubscriptionController.paymentIntentSecret method called: plan id ${id}`,
      );

      if (
        get(request, 'user.userMeta.planId', '') === id &&
        get(request, 'user.userMeta.status', '') === 'active'
      ) {
        return h
          .response({
            statusCode: 409,
            message: 'Subscription already exists',
          })
          .code(409);
      }

      // get plan
      const plan = await planServices.get({ _id: id });
      if (isEmpty(plan)) {
        return h
          .response({
            statusCode: 404,
            message: 'Plan not found',
          })
          .code(404);
      }

      request.logger.info(
        `SubscriptionController.paymentIntentSecret method called: plan ${JSON.stringify(
          plan,
        )}`,
      );

      // Create free user
      if (get(plan, 'slug', '') === 'free') {
        request.logger.info(
          `WebhookController.handleCreateSubscriptionEvent called for create free subscriotion`,
        );
        const duplicateRequest = Object.assign({}, request);
        duplicateRequest.payload = {
          expired: 'NA',
          planId: id,
          amount: 'NA',
          type: get(plan, 'slug', ''),
          status: 'draft',
        };
        await UsersController.updateUserMeta(duplicateRequest);
        request.logger.info(
          `WebhookController.handleCreateSubscriptionEvent update user meta`,
        );
        const subscriptionObj = {
          user: {
            userId: get(request, 'user._id', ''),
            email: get(request, 'user.email', ''),
          },
          planId: id,
          amount: get(plan, 'amount', 0),
          startDate: moment(),
          type: get(plan, 'slug', ''),
          status: 'draft',
        };
        request.logger.info(
          `WebhookController.handleCreateSubscriptionEvent subscription obj for free user ${JSON.stringify(
            subscriptionObj,
          )}`,
        );
        const result = await subscriptionServices.save(subscriptionObj);
        request.logger.info(
          `WebhookController.handleCreateSubscriptionEvent subscription created successfully`,
        );
        await CRMService.trackEvent(
          get(request, 'user._id', ''),
          get(request, 'user.email', ''),
          'subscription_started',
          {
            plan_type: 'free',
            billing_cycle: 'NA',
            name: get(plan, 'name', 'Unknown Plan'),
          },
        );
        return h
          .response({
            statusCode: 200,
            message: 'Free subscription has been created successfully',
            data: result,
          })
          .code(200);
      }

      // get stripe cusomter id
      const userEmail = get(request, 'user.email', '');
      let customerId = get(request, 'user.userMeta.customerId', ''); // 'cus_RGLhrWdTbL8irl';
      if (isEmpty(customerId)) {
        request.logger.info(
          `SubscriptionController.paymentIntentSecret customerId is missing ${userEmail}`,
        );
        const createCustomerPayload = {
          id: get(request, 'user._id', ''),
          email: userEmail,
          name: get(request, 'user.profile.name.fullName', ''),
        };
        const stripeCustomer = await Stripe.createCustomer(
          createCustomerPayload,
        );
        if (get(stripeCustomer, 'id', '') === '') {
          return h
            .response({
              statusCode: 404,
              message: 'Failed to create stripe customer',
            })
            .code(404);
        }
        // update user in IM
        const duplicateRequest = Object.assign({}, request);
        duplicateRequest.payload = { customerId: stripeCustomer.id };
        await UsersController.updateUserMeta(duplicateRequest);

        // Save payment request
        customerId = stripeCustomer.id;
        request.logger.info(
          `SubscriptionController.paymentIntentSecret customerId is generated ${customerId} for ${userEmail}`,
        );
      } else {
        request.logger.info(
          `SubscriptionController.paymentIntentSecret customerId is ${customerId}`,
        );
      }
      const productPrice = find(plan.productPrice, { priceId });
      if (get(plan, 'slug', '') === 'trial') {
        const duplicateRequest = Object.assign({}, request);
        request.logger.info(
          `SubscriptionController.paymentIntentSecret trial create for customer id ${customerId}`,
        );
        const userMeta = get(request, 'user.userMeta', '');
        if (get(userMeta, 'type', '') !== '') {
          return h
            .response({
              statusCode: 400,
              message: `Can't create trial for email ${userEmail}`,
              data: {},
            })
            .code(400);
        }
        duplicateRequest.payload = {
          billing: productPrice.billing,
          userId: get(request, 'user._id', ''),
          customerId,
          status: 'active',
          planId: id,
          type: 'trial',
          cancelAtPeriodEnd: true,
          amount: 0,
        };
        await UsersController.updateUserMeta(duplicateRequest);
        const trialSubscription = await Stripe.createSubscription({
          customer: customerId,
          items: [{ price: productPrice.priceId }],
          trial_period_days: parseInt(process.env.STRIPE_TRIAL_DAYS),
          metadata: {
            billing: productPrice.billing,
            remainingDays: 0,
            planId: id,
            name: get(plan, 'name', 'Unknown Plan'),
            priceId: productPrice.priceId,
            productId: plan.productId,
            planSlug: plan.slug,
            email: userEmail,
            userId: get(request, 'user._id', ''),
          },
        });
        request.logger.info(
          `SubscriptionController.paymentIntentSecret trial has been created for customer id ${customerId} subscriotion data ${JSON.stringify(
            trialSubscription,
          )}`,
        );
        const updateSubscription = await Stripe.updateSubscription(
          trialSubscription.id,
          {
            cancel_at_period_end: true,
          },
        );
        await CRMService.trackEvent(
          get(request, 'user._id', ''),
          userEmail,
          'subscription_started',
          {
            plan_type: plan.slug,
            billing_cycle: productPrice.billing,
            name: get(plan, 'name', 'Unknown Plan'),
          },
        );
        request.logger.info(
          `SubscriptionController.paymentIntentSecret trial has been updated for customer id ${customerId} subscriotion data ${JSON.stringify(
            updateSubscription,
          )}`,
        );
        return h
          .response({
            statusCode: 200,
            message: 'Trial has been created successfully',
            data: trialSubscription,
          })
          .code(200);
      }
      const trialDays = await Stripe.getCustomerSubscription({
        customer: customerId,
      });
      const activeSubscription = find(trialDays.data, (item) =>
        includes(['active', 'trialing'], item.status),
      );
      const incompleteSubscription = find(trialDays.data, (item) =>
        includes(['incomplete'], item.status),
      );
      if (!isEmpty(incompleteSubscription)) {
        await Stripe.cancelSubsctiption(incompleteSubscription.id);
      }
      const createSubscription = await Stripe.createSubscription({
        customer: customerId,
        items: [
          {
            price: productPrice.priceId,
          },
        ],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          billing: productPrice.billing,
          ...(get(activeSubscription, 'current_period_end') && {
            remainingDays: get(activeSubscription, 'current_period_end'),
          }),
          planId: id,
          name: get(plan, 'name', 'Unknown Plan'),
          priceId: productPrice.priceId,
          productId: plan.productId,
          planSlug: plan.slug,
          email: userEmail,
          userId: get(request, 'user._id', ''),
        },
      });

      await CRMService.trackEvent(
        get(request, 'user._id', ''),
        userEmail,
        'subscription_started',
        {
          plan_type: plan.slug,
          billing_cycle: productPrice.billing,
          name: get(plan, 'name', 'Unknown Plan'),
        },
      );
      const paymentIntentResp = {
        id: createSubscription.id,
        clientSecret:
          createSubscription.latest_invoice.payment_intent.client_secret,
        customer: customerId,
        amount: productPrice.amount,
        currency: 'gbp',
      };

      return h
        .response({
          statusCode: 200,
          message: 'Payment intent created successfully',
          data: paymentIntentResp,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        `SubscriptionController.paymentIntentSecret method error ${err}`,
      );
      return errorHandler(err);
    }
  }

  static async get(request, h) {
    try {
      const _id = request.params.id;
      request.logger.info(`SubscriptionController.get method called ${_id}`);

      const result = await subscriptionServices.get({ _id });
      if (isEmpty(result)) {
        return h
          .response({
            statusCode: 404,
            message: 'Subscription not found',
            data: result,
          })
          .code(404);
      }
      request.logger.info(
        'SubscriptionController.get method called successfully',
      );
      return h
        .response({
          statusCode: 200,
          message: 'Subscription data get successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(`SubscriptionController.get method error ${err}`);
      return errorHandler(err);
    }
  }

  /**
   * Generates a Stripe Customer Portal session link for managing subscriptions, invoices, and payment methods.
   * This endpoint creates a secure, time-limited URL (30 min validity) for users to access the Stripe Customer Portal.
   *
   * @param {object} request - Hapi.js request object
   * @param {object} request.params - The parameters in the request
   * @param {object} h - Hapi.js response toolkit
   * @returns {object} - Returns a JSON object with a URL for the Stripe Customer Portal
   * @todo - improve this function to streamline process
   * @throws {Error} - Returns an error response if the portal link creation fails
   * <AUTHOR>
   */
  static async getStripeSubsriptionLink(request, h) {
    try {
      const { user } = request;
      request.logger.info(
        `SubscriptionController.getStripeSubsriptionLink method called for user ${user._id}`,
      );

      const stripeCustomerId = get(user, 'userMeta.customerId', '');
      if (isEmpty(stripeCustomerId)) {
        request.logger.info(
          `SubscriptionController.getStripeSubsriptionLink customer id is missing for user ${user._id}`,
        );
        return h
          .response({
            statusCode: 404,
            message: 'Subscription is not found.',
          })
          .code(404);
      }
      const url =
        await StripePaymentsService.getStripeBillingPortalUrl(stripeCustomerId);
      request.logger.info(
        `SubscriptionController.getStripeSubsriptionLink stripe subscription url is ${url} for user ${user._id}`,
      );
      return h
        .response({
          statusCode: 200,
          message: 'Subscription has been fetched successfully.',
          data: { url },
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        err,
        'Error in SubscriptionController.getStripeSubsriptionLink',
      );
      errorHandler(err);
    }
  }

  /**
   * Generates a Stripe Checkout session link for purchasing subscriptions.
   * This function creates a secure URL that customers can use to complete their subscription purchase.
   *
   * @param {string} customerId - The unique identifier for the customer in Stripe.
   * @param {string} priceId - The unique identifier for the subscription price in Stripe.
   * @returns {Promise<string>} - Returns a promise that resolves to a URL for the Stripe Checkout session.
   * @throws {Error} - Throws an error if the Checkout session creation fails, with details on the failure.
   * <AUTHOR>
   */
  static async getStripeCheckoutLink(request, h) {
    try {
      const { payload } = request;
      request.logger.info(
        `SubscriptionController.getStripeCheckoutLink method called for user`,
      );

      const email = get(payload, 'email', '');

      const userData = await userService.getUserList({ email }, email);
      if (isEmpty(userData)) {
        request.logger.info(
          `SubscriptionController.getStripeCheckoutLink user not exist`,
        );
        return h
          .response({
            statusCode: 404,
            message: 'Subscription is not found.',
          })
          .code(404);
      }

      const headObject = head(userData.docs);
      let customerId = get(headObject, 'userMeta.customerId', null);
      const planData = await planServices.get({ _id: payload.planId });
      const productPrice = find(planData.productPrice, {
        priceId: payload.priceId,
      });

      const productPriceId = get(productPrice, 'priceId', null);
      if (!productPriceId) {
        request.logger.info(
          `SubscriptionController.getStripeCheckoutLink product price id is missing for user`,
        );
        return h
          .response({
            statusCode: 404,
            message: 'Subscription is not found.',
          })
          .code(404);
      }
      if (!customerId) {
        const createCustomer =
          await StripePaymentsService.createCustomer(headObject);
        customerId = createCustomer.id;
      }
      let remainingDays = 0;
      const user = get(headObject, 'userMeta', '');
      if (
        (get(user, 'type', '') === 'pro' ||
          get(user, 'type', '') === 'trial') &&
        get(user, 'status', '') === 'active'
      ) {
        remainingDays = get(user, 'expired', 0);
      }

      const metadata = {
        billing: productPrice.billing,
        remainingDays,
        planSlug: payload.type,
        name: get(planData, 'name', 'Unknown Plan'),
        priceId: productPriceId,
        email: payload.email,
        userId: get(headObject, '_id', ''),
        previosSubscription: get(headObject, 'userMeta.subscriptionId', ''),
        planId: payload.planId,
        customerId,
        environment: process.env.APP_ENV,
      };
      const url = await Stripe.getStripeCheckoutLink(
        customerId,
        productPriceId,
        metadata,
      );
      request.logger.info(
        `SubscriptionController.getStripeCheckoutLink stripe subscription url is ${url} for user`,
      );
      return h
        .response({
          statusCode: 200,
          message: 'Subscription has been fetched successfully.',
          data: { url },
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        err,
        'Error in SubscriptionController.getStripeCheckoutLink',
      );
      errorHandler(err);
    }
  }

  /**
   * Retrieve customer payment intent using customer id.
   * This function get payment intent for customer.
   *
   * @param {object} request - Hapi.js request object
   * @returns {Promise<string>} - Returns a promise that resolves to customer payment intent.
   * @throws {Error} - Throws an error if the payment intent list fails, with details on the failure.
   */
  static async customerPaymentIntent(request, h) {
    try {
      request.logger.info(
        'SubscriptionController.customerPaymentIntent method called',
      );
      const { user } = request;
      const customerId = get(user, 'userMeta.customerId', '');
      if (customerId === '') {
        request.logger.info(
          `SubscriptionController.customerPaymentIntent customer id not found for ${user.email}`,
        );
        return h
          .response({
            statusCode: 404,
            message: 'Customer id not found',
            data: {},
          })
          .code(404);
      }
      request.logger.info(
        `SubscriptionController.customerPaymentIntent retrieve payment intent for customer id ${customerId}`,
      );

      const result = await Stripe.customerPaymentIntent(customerId);
      request.logger.info(
        `SubscriptionController.customerPaymentIntent payment intent retrieve successfully, data - ${JSON.stringify(
          result,
        )}`,
      );
      return h
        .response({
          statusCode: 200,
          message: 'Customer payment intent retrieve successfully',
          data: result,
        })
        .code(200);
    } catch (err) {
      request.logger.error(
        `SubscriptionController.customerPaymentIntent method error ${err}`,
      );
      return errorHandler(err);
    }
  }

  static async createSubscription(request, plan, priceId, couponId) {
    try {
      const userEmail = get(request, 'user.email', '');
      const customerId = get(request, 'user.userMeta.customerId', '');
      const productPrice = find(plan.productPrice, { priceId });
      const trialDays = await Stripe.getCustomerSubscription({
        customer: customerId,
      });
      const activeSubscription = find(trialDays.data, (item) =>
        includes(['active', 'trialing'], item.status),
      );
      const incompleteSubscription = find(trialDays.data, (item) =>
        includes(['incomplete'], item.status),
      );
      if (!isEmpty(incompleteSubscription)) {
        await Stripe.cancelSubsctiption(incompleteSubscription.id);
      }
      const createSubscription = await Stripe.createSubscription({
        customer: customerId,
        items: [
          {
            price: productPrice.priceId,
          },
        ],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        ...(couponId && {
          discounts: [
            {
              promotion_code: couponId,
            },
          ],
        }),
        metadata: {
          billing: productPrice.billing,
          ...(get(activeSubscription, 'current_period_end') && {
            remainingDays: get(activeSubscription, 'current_period_end'),
          }),
          planId: plan.id,
          name: get(plan, 'name', 'Unknown Plan'),
          priceId: productPrice.priceId,
          productId: plan.productId,
          planSlug: plan.slug,
          email: userEmail,
          userId: get(request, 'user._id', ''),
        },
      });

      return {
        id: createSubscription.id,
        clientSecret:
          createSubscription.latest_invoice.payment_intent.client_secret,
        customer: customerId,
        amount: productPrice.amount,
        currency: 'gbp',
      };
    } catch (err) {
      request.logger.error(
        `SubscriptionController.createSubscription method called ${plan.id}`,
      );
    }
  }

  static async applyCouponCode(request, h) {
    try {
      const { couponCode, planId, priceId } = request.payload;
      const plan = await planServices.get({ _id: planId });

      // Retrieve the coupon details
      const promo = await Stripe.retrievePromotionCodes({
        code: couponCode, // Match promo code
        active: true, // Ensure it's active
      });

      if (
        get(promo, 'deleted', null) !== null ||
        isEmpty(get(promo, 'data', null))
      ) {
        return h
          .response({
            statusCode: 400,
            message: 'Invalid promotional code.',
            data: {},
          })
          .code(400);
      }
      const productPrice = find(plan.productPrice, {
        priceId,
      });
      const promoCodeDetails = get(promo, 'data[0]', {});
      const couponDetails = get(promoCodeDetails, 'coupon', {});
      const couponMetadata = get(couponDetails, 'metadata', {});
      if (!isEmpty(couponMetadata)) {
        const validPlanIds = get(couponMetadata, 'priceId', []);
        if (!validPlanIds.includes(priceId)) {
          return h
            .response({
              statusCode: 400,
              message: `This promotional code is not valid for the ${productPrice.billing} plan.`,
              data: {},
            })
            .code(400);
        }
      }

      // const subscriptionObj = await Stripe.getSubscription(subscriptionId);
      plan.id = planId;
      const subscriptionObj = await SubscriptionController.createSubscription(
        request,
        plan,
        priceId,
        get(promoCodeDetails, 'id', ''),
      );
      // // Calculate discounted amount
      const originalAmount = get(subscriptionObj, 'amount', 0); // Use the original amount from the PaymentIntent
      let finalAmount = originalAmount;
      if (get(couponDetails, 'percent_off', null) !== null) {
        const discountAmountCents = Math.round(
          (originalAmount * couponDetails.percent_off) / 100,
        );
        finalAmount = originalAmount - discountAmountCents;
      } else if (get(couponDetails, 'amount_off', null) !== null) {
        finalAmount = originalAmount - Number(couponDetails.amount_off);
      }
      return h
        .response({
          statusCode: 200,
          message: 'Customer payment intent updated successfully',
          data: {
            ...subscriptionObj,
            amount: finalAmount,
            couponDetails:
              get(couponDetails, 'percent_off', null) !== null
                ? {
                    type: 'percent_off',
                    amount: get(couponDetails, 'percent_off', null),
                  }
                : {
                    type: 'amount_off',
                    amount: get(couponDetails, 'amount_off', null),
                  },
          },
        })
        .code(200);
    } catch (error) {
      request.logger.error(
        `SubscriptionController.applyCouponCode method error ${error}`,
      );
      return h
        .response({
          statusCode: 500,
          message: 'An error occurred while applying the coupon.',
          data: {},
        })
        .code(500);
    }
  }

  static async removeCouponCode(request, h) {
    try {
      const { subscriptionId, planId, priceId } = request.payload;
      const plan = await planServices.get({ _id: planId });

      await Stripe.updateSubscription(subscriptionId, {
        discounts: [],
      });

      const subscriptionObj = await SubscriptionController.createSubscription(
        request,
        plan,
        priceId,
        null,
      );
      return h
        .response({
          statusCode: 200,
          message: 'Promotion code removed successfully',
          data: subscriptionObj,
        })
        .code(200);
    } catch (error) {
      console.error('Error removing coupon:', error.message);

      // Handle specific Stripe errors
      if (error.type === 'StripeInvalidRequestError') {
        return h
          .response({
            statusCode: 400,
            message: error.message,
            data: {},
          })
          .code(400);
      }

      // Fallback for unexpected errors
      return h
        .response({
          statusCode: 500,
          message: 'An error occurred while removing the coupon.',
          data: {},
        })
        .code(500);
    }
  }
}

module.exports = SubscriptionController;
